import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import BookingCourtSection from "./booking-court-section";

const BookingTypeSelector = () => {
  return (
    <Tabs defaultValue="book-court" className="container w-full">
      <TabsList className="h-[68px] w-full rounded-[100px] bg-neutral-100 p-2.5">
        <TabsTrigger
          value="book-court"
          className="font-helvetica h-full flex-1 rounded-[30px] text-base font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
        >
          Book a Court
        </TabsTrigger>
        <TabsTrigger
          value="programs"
          className="font-helvetica h-full flex-1 rounded-[30px] text-base font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
        >
          Programs
        </TabsTrigger>
        <TabsTrigger
          value="lessons"
          className="font-helvetica h-full flex-1 rounded-[30px] text-base font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
        >
          Lessons
        </TabsTrigger>
        <TabsTrigger
          value="instructors"
          className="font-helvetica h-full flex-1 rounded-[30px] text-base font-bold tracking-tight data-[state=active]:bg-[#1c5534] data-[state=active]:text-white data-[state=inactive]:text-[#969696]"
        >
          Instructors
        </TabsTrigger>
      </TabsList>

      <TabsContent value="book-court" className="mt-6">
        <BookingCourtSection />
      </TabsContent>

      <TabsContent value="programs" className="mt-6">
        <div className="text-center">
          <p className="text-lg text-gray-600">Programs content will go here</p>
        </div>
      </TabsContent>

      <TabsContent value="lessons" className="mt-6">
        <div className="text-center">
          <p className="text-lg text-gray-600">Lessons content will go here</p>
        </div>
      </TabsContent>

      <TabsContent value="instructors" className="mt-6">
        <div className="text-center">
          <p className="text-lg text-gray-600">Instructors content will go here</p>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default BookingTypeSelector;
