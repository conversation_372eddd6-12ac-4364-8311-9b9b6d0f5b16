import React, { useState, useCallback, useMemo } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Switch } from "@/components/ui/switch";
import { ChevronDown } from "lucide-react";

// Types for the booking form
interface BookingFormData {
  court: string;
  subCourt: string;
  date: string;
  time: string;
  duration: string;
  players: number;
  clutchAi: boolean;
  package: string;
}

const BookingCourtSection = () => {
  // State management for booking form
  const [bookingData, setBookingData] = useState<BookingFormData>({
    court: "",
    subCourt: "",
    date: "",
    time: "",
    duration: "",
    players: 2,
    clutchAi: true, // Default to On
    package: "Allay Package",
  });

  // Optimized handler for updating booking data
  const updateBookingData = useCallback(
    <K extends keyof BookingFormData>(field: K, value: BookingFormData[K]) => {
      setBookingData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  // Memoized clutch AI status text
  const clutchAiStatus = useMemo(() => {
    return bookingData.clutchAi ? "On" : "Off";
  }, [bookingData.clutchAi]);

  // Handler for clutch AI toggle
  const handleClutchAiChange = useCallback(
    (enabled: boolean) => {
      updateBookingData("clutchAi", enabled);
    },
    [updateBookingData]
  );
  return (
    <div className="container py-2">
      <div className="inline-flex w-full flex-col items-start justify-start gap-10">
        <div className="inline-flex items-start justify-between self-stretch rounded-[30px] bg-white px-[60px] py-10">
          <div className="size- inline-flex flex-col items-start justify-start gap-4">
            <div className="font-helvetica justify-start text-base leading-[35px] font-normal text-black">
              Select Court
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="inline-flex w-[518px] items-center justify-between rounded-[20px] bg-white px-[25px] py-2 outline-1 outline-[#c3c3c3] transition-colors hover:bg-gray-50">
                <span className="font-helvetica h-[32.67px] w-[207.45px] justify-start text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.court || "Select court"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[518px] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {["Court 1", "Court 2", "Court 3", "Court 4"].map((court) => (
                  <DropdownMenuItem
                    key={court}
                    onClick={() => updateBookingData("court", court)}
                    className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                  >
                    <span className="font-helvetica text-[15px] font-normal text-black">
                      {court}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            <div className="font-helvetica justify-start text-base leading-[35px] font-normal text-black">
              Select Date
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="inline-flex w-[518px] items-center justify-between rounded-[20px] bg-white px-[25px] py-2 outline-1 outline-[#c3c3c3] transition-colors hover:bg-gray-50">
                <span className="font-helvetica h-[32.67px] w-[207.45px] justify-start text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.date || "Select date"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[518px] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {["Today", "Tomorrow", "Wed 25 April", "Thu 26 April", "Fri 27 April"].map(
                  (date) => (
                    <DropdownMenuItem
                      key={date}
                      onClick={() => updateBookingData("date", date)}
                      className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                    >
                      <span className="font-helvetica text-[15px] font-normal text-black">
                        {date}
                      </span>
                    </DropdownMenuItem>
                  )
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <div className="inline-flex w-[515.17px] items-center justify-between">
              <div className="font-helvetica justify-start text-center text-base leading-[35px] font-normal text-black">
                Select Time
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="inline-flex w-[518px] items-center justify-between rounded-[20px] bg-white px-[25px] py-2 outline-1 outline-[#c3c3c3] transition-colors hover:bg-gray-50">
                <span className="font-helvetica w-[207.45px] justify-start text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.time || "Select time"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[518px] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {[
                  "9:00 AM",
                  "10:00 AM",
                  "11:00 AM",
                  "12:00 PM",
                  "1:00 PM",
                  "2:00 PM",
                  "3:00 PM",
                  "4:00 PM",
                  "5:00 PM",
                ].map((time) => (
                  <DropdownMenuItem
                    key={time}
                    onClick={() => updateBookingData("time", time)}
                    className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                  >
                    <span className="font-helvetica text-[15px] font-normal text-black">
                      {time}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            <div className="inline-flex w-[515.17px] items-center justify-between">
              <div className="font-helvetica justify-start text-center text-base leading-[35px] font-normal text-black">
                Select Duration
              </div>
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger className="inline-flex w-[518px] items-center justify-between rounded-[20px] bg-white px-[25px] py-2 outline-1 outline-[#c3c3c3] transition-colors hover:bg-gray-50">
                <span className="font-helvetica h-[32.67px] w-[207.45px] justify-start text-[15px] leading-[35px] font-normal text-[#c3c3c3]">
                  {bookingData.duration || "Select duration"}
                </span>
                <ChevronDown className="h-4 w-4 text-[#c3c3c3]" />
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[518px] rounded-[20px] border border-[#c3c3c3] bg-white shadow-lg">
                {["30 minutes", "60 minutes", "90 minutes", "120 minutes"].map((duration) => (
                  <DropdownMenuItem
                    key={duration}
                    onClick={() => updateBookingData("duration", duration)}
                    className="cursor-pointer px-[25px] py-3 hover:bg-gray-50"
                  >
                    <span className="font-helvetica text-[15px] font-normal text-black">
                      {duration}
                    </span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {/* <div data-svg-wrapper>
            <svg
              width="2"
              height="640"
              viewBox="0 0 2 640"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2 1C2 0.447715 1.55228 2.41411e-08 1 0C0.447715 -2.41411e-08 2.41411e-08 0.447715 0 1L2 1ZM1 1L0 1L-2.79024e-05 639.333L0.999972 639.333L1.99997 639.333L2 1L1 1Z"
                fill="#EBEBEB"
              />
            </svg>
          </div> */}
          <div className="inline-flex w-[518px] flex-col items-start justify-between self-stretch">
            <div className="flex flex-col items-start justify-start gap-[30px] self-stretch">
              <div className="font-helvetica justify-start text-base leading-[35px] font-normal text-black">
                Number of Players
              </div>
              <div data-svg-wrapper>
                <svg
                  width="42"
                  height="42"
                  viewBox="0 0 42 42"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="21" cy="21" r="20.5" fill="white" stroke="#D8D8D8" />
                </svg>
              </div>
              <div data-svg-wrapper>
                <svg
                  width="17"
                  height="2"
                  viewBox="0 0 17 2"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M1 1H16" stroke="#D8D8D8" stroke-width="2" stroke-linecap="round" />
                </svg>
              </div>
              <div data-svg-wrapper>
                <svg
                  width="42"
                  height="42"
                  viewBox="0 0 42 42"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="21" cy="21" r="20.5" fill="white" stroke="#D8D8D8" />
                </svg>
              </div>
              <div data-svg-wrapper>
                <svg
                  width="17"
                  height="16"
                  viewBox="0 0 17 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 7.51163H16M8.17391 1V15"
                    stroke="black"
                    stroke-width="2"
                    stroke-linecap="round"
                  />
                </svg>
              </div>
              <div className="font-helvetica w-[87px] justify-start text-center text-[15px] leading-[35px] font-normal text-black">
                2
              </div>
              <div className="inline-flex h-[108px] w-[401px] items-start justify-start gap-[141px]">
                <div className="inline-flex w-[514px] flex-col items-start justify-start gap-2">
                  <div className="flex items-center justify-between">
                    <div className="font-helvetica justify-start text-base leading-[35px] font-normal text-black">
                      Clutch Ai
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="font-helvetica text-[14px] font-normal text-[#1c5534]">
                        {clutchAiStatus}
                      </span>
                      <Switch
                        checked={bookingData.clutchAi}
                        onCheckedChange={(checked) => handleClutchAiChange(checked)}
                        className="data-[state=checked]:bg-[#ddba0a] data-[state=unchecked]:bg-gray-300"
                      />
                    </div>
                  </div>
                  <div className="font-helvetica justify-start self-stretch text-[13px] leading-snug font-normal text-black">
                    The Clutch Cam automates content creation from your courts — generating
                    Instagrammable highlight reels & performance stats for every game.
                  </div>
                </div>
              </div>
              <div data-svg-wrapper data-size="24" className="relative">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g clip-path="url(#clip0_2951_3055)">
                    <path
                      d="M12 16V12M12 8H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                      stroke="black"
                      stroke-width="2.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_2951_3055">
                      <rect width="24" height="24" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
              <div className="h-[34px] w-[65px] rounded-[18.50px] bg-[#fffaed]" />
              <div data-svg-wrapper>
                <svg
                  width="36"
                  height="36"
                  viewBox="0 0 36 36"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g filter="url(#filter0_d_2951_3058)">
                    <rect x="4" y="2" width="28" height="28" rx="14" fill="#DDBA0A" />
                  </g>
                  <defs>
                    <filter
                      id="filter0_d_2951_3058"
                      x="0"
                      y="0"
                      width="36"
                      height="36"
                      filterUnits="userSpaceOnUse"
                      color-interpolation-filters="sRGB"
                    >
                      <feFlood flood-opacity="0" result="BackgroundImageFix" />
                      <feColorMatrix
                        in="SourceAlpha"
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha"
                      />
                      <feOffset dy="2" />
                      <feGaussianBlur stdDeviation="2" />
                      <feComposite in2="hardAlpha" operator="out" />
                      <feColorMatrix
                        type="matrix"
                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"
                      />
                      <feBlend
                        mode="normal"
                        in2="BackgroundImageFix"
                        result="effect1_dropShadow_2951_3058"
                      />
                      <feBlend
                        mode="normal"
                        in="SourceGraphic"
                        in2="effect1_dropShadow_2951_3058"
                        result="shape"
                      />
                    </filter>
                  </defs>
                </svg>
              </div>
              <div className="inline-flex items-center justify-between self-stretch rounded-[20px] bg-[#fffaed] px-[25px] py-2 outline outline-1 outline-[#ddba0a]">
                <div className="font-helvetica h-[32.67px] w-[207.45px] justify-start text-[15px] leading-[35px] font-normal text-[#1c5534]">
                  Allay Package{" "}
                </div>
                <div data-svg-wrapper className="relative">
                  <svg
                    width="31"
                    height="31"
                    viewBox="0 0 31 31"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15.789 19.6436L8.18359 12.0383L9.95818 10.2637L15.789 16.0944L21.6197 10.2637L23.3943 12.0383L15.789 19.6436Z"
                      fill="#DDBA0A"
                    />
                  </svg>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-start justify-start gap-5 self-stretch">
              <div className="inline-flex items-end justify-between self-stretch rounded-[20px] border-[#ddba0a] bg-[#fffaed] px-6 py-2.5 shadow-[0px_-4px_20px_0px_rgba(0,0,0,0.06)]">
                <div className="justify-start">
                  <span className="font-['IBM_Plex_Sans'] text-base leading-snug font-medium text-black">
                    Booking Details
                    <br />
                  </span>
                  <span className="font-['IBM_Plex_Sans'] text-[15px] leading-[21px] font-normal text-black">
                    Court 2<br />
                    Wed 25 April
                    <br />
                    1:30PM - 60 mins
                    <br />2 Players
                    <br />
                    Clutch Ai - {clutchAiStatus}
                  </span>
                </div>
                <div className="size- flex items-center justify-center gap-2.5 py-2.5 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]">
                  <div className="justify-start text-center font-['IBM_Plex_Sans'] text-[22px] leading-normal font-medium text-[#1c5534]">
                    Pay $70
                  </div>
                </div>
              </div>
              <div className="inline-flex items-center justify-between self-stretch rounded-[41px] bg-[#ddba0a] px-10 py-2.5 shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]">
                <div className="font-helvetica justify-start text-center text-base leading-none font-bold text-white">
                  Book Now
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingCourtSection;
