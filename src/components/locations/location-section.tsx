"use client";
import { Location } from "@/app/locations/[slug]/page";
import React, { useRef } from "react";
import {
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
  Accordion,
} from "@/components/ui/accordion";
import BookingSection from "../booking/booking-section";

const LocationSection = ({ location }: { location: Location }) => {
  const mapSectionRef = useRef<HTMLDivElement>(null);
  return (
    <>
      {/* Banner Section */}
      <section
        className="relative h-[500px] bg-cover bg-center lg:h-[600px]"
        style={{ backgroundImage: `url(${location.bannerImage})` }}
      >
        <div className="absolute inset-0 bg-black/50" />
        <div className="relative container mx-auto h-full px-4">
          <div className="flex h-full items-center">
            <div className="w-full lg:w-5/12">
              <h1 className="mt-12 text-4xl font-bold text-white md:text-5xl">{location.name}</h1>
              {/* <div className="mt-5">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    if (mapSectionRef.current) {
                      mapSectionRef.current.scrollIntoView({ behavior: "smooth", block: "center" });
                    }
                  }}
                  className="hover:bg-opacity-90 font-helvetica inline-block rounded-full bg-white px-6 py-3 font-medium text-[#1c5534] transition-all"
                >
                  View Location
                </button>
              </div> */}
            </div>
          </div>
        </div>
      </section>

      {/* Overview Section */}
      <Accordion
        type="single"
        collapsible
        className="container mx-auto w-full px-4 pt-6 pb-20"
        defaultValue="item-1"
      >
        <AccordionItem value="item-1" className="border-0">
          <AccordionTrigger className="font-helvetica hover:text-primary mb-6 border-b-2 border-[#CCBF9D] text-4xl font-bold text-black hover:no-underline">
            {location.title}
          </AccordionTrigger>
          <AccordionContent className="w-full">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-12">
              <div className="lg:col-span-7">
                {/* <p className="text-xl leading-relaxed text-gray-700">{location.description}</p> */}
                <div
                  id="location-description"
                  className="flex flex-col gap-2 text-xl leading-relaxed text-gray-700"
                  dangerouslySetInnerHTML={{ __html: location.description }}
                ></div>
              </div>
              <div className="lg:col-span-5">
                <div className="overflow-hidden rounded-lg shadow-lg">
                  <iframe
                    src={location.mapUrl}
                    width="100%"
                    height="462"
                    style={{ border: "none" }}
                    allowFullScreen
                  />
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <section className="container mx-auto w-full px-4 pb-20">
        <BookingSection />
      </section>
    </>
  );
};

export default LocationSection;
